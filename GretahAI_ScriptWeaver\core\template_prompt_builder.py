"""
Prompt Builder for Script Playground (Stage 10).

This module provides specialized prompt generation for creating test automation scripts
using optimized scripts as templates. It focuses on preserving the template's structure,
patterns, and best practices while adapting the content for new test cases.
"""

import logging
from typing import Dict, Any, List, Optional

# Set up logging
logger = logging.getLogger(__name__)

# Note: Template-based generation uses pre-validated scripts from Stages 1-8
# which already have resolved locators, so locator conflict resolution is not needed here


def generate_template_based_script_prompt(
    template_script: Dict[str, Any],
    target_test_case: Dict[str, Any],
    template_structure_info: Dict[str, Any],
    website_url: str = None
) -> str:
    """
    Generate a comprehensive prompt for template-based test script generation.

    Args:
        template_script: The optimized script to use as template
        target_test_case: The test case to generate script for
        template_structure_info: Structural analysis of the template
        website_url: Target website URL

    Returns:
        str: The generated prompt for AI script generation
    """
    try:
        # Extract template information
        template_content = template_script.get('content', '')
        template_test_case_id = template_script.get('test_case_id', 'Unknown')

        # Extract target test case information
        target_tc_id = target_test_case.get('Test Case ID', 'Unknown')
        target_objective = target_test_case.get('Test Case Objective', 'No objective specified')
        target_steps = target_test_case.get('Steps', [])

        # Build the prompt
        prompt = f"""# Template-Based Test Script Generation

## Task
Generate a new PyTest automation script for the target test case using the provided optimized script as a template.
Preserve the template's structure, patterns, and best practices while adapting the content for the new test case.

## Template Script Information
- **Original Test Case ID**: {template_test_case_id}
- **Template Type**: Optimized Script (proven structure and patterns)
- **Template Size**: {template_structure_info.get('total_lines', 0)} lines
- **Test Functions**: {len(template_structure_info.get('test_functions', []))}
- **Helper Functions**: {len(template_structure_info.get('helper_functions', []))}
- **Locator Strategies**: {', '.join(template_structure_info.get('locator_strategies', []))}
- **Has Browser Setup**: {template_structure_info.get('has_browser_setup', False)}
- **Has Error Handling**: {template_structure_info.get('has_error_handling', False)}
- **Has Assertions**: {template_structure_info.get('has_assertions', False)}

## Target Test Case
- **Test Case ID**: {target_tc_id}
- **Objective**: {target_objective}
- **Number of Steps**: {len(target_steps)}
{_format_target_steps(target_steps)}

## Template Script (Reference)
```python
{template_content}
```

## Generation Instructions

### 1. Structure Preservation
- Maintain the same overall script structure as the template
- Preserve import statements and fixture definitions
- Keep the same function organization pattern
- Maintain error handling and browser setup patterns

### 2. Content Adaptation
- Replace template test case logic with target test case requirements
- Update function names to reflect the new test case ID
- Adapt step implementations to match target test case steps
- Update assertions to match target test case expected results

### 3. Locator Strategy Consistency
- Use the same locator strategies as the template where applicable
- Maintain the template's preference for CSS selectors, XPath, etc.
- Follow the template's element identification patterns

### 4. Best Practices Retention
- Preserve all optimization patterns from the template
- Maintain the template's wait strategies and timeouts
- Keep the template's error handling approaches
- Retain the template's assertion patterns

### 5. Test Data Integration
- Adapt any test data patterns from the template
- Ensure test data is properly integrated into the new script
- Maintain the template's data handling approaches

### 6. Browser Management
- Preserve the template's browser initialization and cleanup
- Maintain the template's page navigation patterns
- Keep the template's browser state management

## Quality Requirements
- The generated script must be syntactically correct Python/PyTest code
- All imports must be properly included
- Function names should follow the pattern: test_{target_tc_id.lower().replace(' ', '_')}_functionality
- Include comprehensive error handling as shown in the template
- Ensure proper browser cleanup and resource management
- Add meaningful assertions for each verification step

## Output Format
Return only the complete Python script code without any additional explanation or markdown formatting.
The script should be ready to execute as a PyTest test case.

## Website Context
{f"Target Website: {website_url}" if website_url else "No specific website URL provided"}

Generate the new test script now, ensuring it follows the template's proven patterns while implementing the target test case requirements.
"""

        logger.info(f"Generated template-based script prompt for {target_tc_id} using template from {template_test_case_id}")
        return prompt

    except Exception as e:
        logger.error(f"Failed to generate template-based script prompt: {e}")
        return _generate_fallback_prompt(target_test_case)


def _format_target_steps(steps: List[Dict[str, Any]]) -> str:
    """
    Format target test case steps for inclusion in the prompt.

    Args:
        steps: List of test case steps

    Returns:
        Formatted string representation of steps
    """
    try:
        if not steps:
            return "- No steps defined"

        formatted_steps = []
        for i, step in enumerate(steps, 1):
            action = step.get('action', 'No action specified')
            expected = step.get('expected_result', 'No expected result specified')

            # Truncate long descriptions
            if len(action) > 100:
                action = action[:97] + "..."
            if len(expected) > 100:
                expected = expected[:97] + "..."

            formatted_steps.append(f"  {i}. **Action**: {action}")
            formatted_steps.append(f"     **Expected**: {expected}")

        return "\n".join(formatted_steps)

    except Exception as e:
        logger.error(f"Failed to format target steps: {e}")
        return "- Error formatting steps"


def _generate_fallback_prompt(target_test_case: Dict[str, Any]) -> str:
    """
    Generate a fallback prompt when template-based generation fails.

    Args:
        target_test_case: The target test case

    Returns:
        Basic prompt for script generation
    """
    try:
        target_tc_id = target_test_case.get('Test Case ID', 'Unknown')
        target_objective = target_test_case.get('Test Case Objective', 'No objective specified')

        return f"""# Fallback Test Script Generation

## Task
Generate a PyTest automation script for the following test case.

## Test Case Information
- **Test Case ID**: {target_tc_id}
- **Objective**: {target_objective}

## Instructions
Create a basic PyTest script that implements the test case requirements.
Include proper imports, browser setup, and error handling.

Generate the Python script code now.
"""

    except Exception as e:
        logger.error(f"Failed to generate fallback prompt: {e}")
        return "Generate a basic PyTest automation script."


def generate_template_comparison_prompt(
    template_script: Dict[str, Any],
    generated_script: str,
    target_test_case: Dict[str, Any]
) -> str:
    """
    Generate a prompt for comparing template and generated scripts.

    Args:
        template_script: The original template script
        generated_script: The newly generated script
        target_test_case: The target test case

    Returns:
        Prompt for script comparison analysis
    """
    try:
        template_content = template_script.get('content', '')
        template_test_case_id = template_script.get('test_case_id', 'Unknown')
        target_tc_id = target_test_case.get('Test Case ID', 'Unknown')

        prompt = f"""# Template vs Generated Script Comparison

## Task
Analyze the generated script against the template to ensure proper adaptation while preserving best practices.

## Template Script (Original - {template_test_case_id})
```python
{template_content}
```

## Generated Script (New - {target_tc_id})
```python
{generated_script}
```

## Analysis Requirements
1. **Structure Preservation**: Verify the generated script maintains the template's overall structure
2. **Pattern Consistency**: Check that optimization patterns from the template are preserved
3. **Content Adaptation**: Confirm the script properly implements the target test case requirements
4. **Quality Assurance**: Identify any syntax errors or missing components

## Output Format
Provide a brief analysis focusing on:
- Structure preservation (Yes/No with brief explanation)
- Pattern consistency (Yes/No with brief explanation)
- Content adaptation quality (Good/Fair/Poor with brief explanation)
- Any critical issues found

Keep the analysis concise and actionable.
"""

        return prompt

    except Exception as e:
        logger.error(f"Failed to generate template comparison prompt: {e}")
        return "Analyze the generated script for quality and consistency."


def enhance_template_prompt_with_context(
    base_prompt: str,
    additional_context: Dict[str, Any]
) -> str:
    """
    Enhance the template-based prompt with additional context including gap analysis.

    Args:
        base_prompt: The base template prompt
        additional_context: Additional context information

    Returns:
        Enhanced prompt with additional context
    """
    try:
        enhancements = []

        # Add browser context if available
        if additional_context.get('browser_type'):
            enhancements.append(f"Target Browser: {additional_context['browser_type']}")

        # Add environment context if available
        if additional_context.get('test_environment'):
            enhancements.append(f"Test Environment: {additional_context['test_environment']}")

        # Add custom instructions if available
        if additional_context.get('custom_instructions'):
            enhancements.append(f"Custom Instructions: {additional_context['custom_instructions']}")

        # Add gap analysis context if available
        gap_analysis_context = additional_context.get('gap_analysis')
        gap_responses = additional_context.get('gap_responses')

        if gap_analysis_context and gap_responses:
            gap_section = _build_gap_analysis_enhancement_section(gap_analysis_context, gap_responses)
            if gap_section:
                enhancements.append(gap_section)

        if enhancements:
            enhancement_section = "\n## Additional Context\n" + "\n".join(f"- {enhancement}" for enhancement in enhancements)
            # Insert before the final generation instruction
            insertion_point = base_prompt.rfind("Generate the new test script now")
            if insertion_point != -1:
                enhanced_prompt = (
                    base_prompt[:insertion_point] +
                    enhancement_section + "\n\n" +
                    base_prompt[insertion_point:]
                )
                return enhanced_prompt

        return base_prompt

    except Exception as e:
        logger.error(f"Failed to enhance template prompt with context: {e}")
        return base_prompt


def _build_gap_analysis_enhancement_section(gap_analysis_data: Dict[str, Any], gap_responses: Dict[str, Any]) -> str:
    """
    Build the gap analysis enhancement section for the prompt.

    Args:
        gap_analysis_data: Gap analysis results from AI
        gap_responses: User responses to fill gaps

    Returns:
        str: Gap analysis enhancement section
    """
    try:
        gap_section = "Gap Analysis Results and User Responses:\n"

        # Add overall assessment
        overall_assessment = gap_analysis_data.get('overall_assessment', '')
        if overall_assessment:
            gap_section += f"  - Overall Assessment: {overall_assessment}\n"

        # Add compatibility score
        compatibility_score = gap_analysis_data.get('compatibility_score', '')
        if compatibility_score:
            gap_section += f"  - Compatibility Score: {compatibility_score}%\n"

        # Add identified gaps and user responses
        gaps = gap_analysis_data.get('gaps', [])
        if gaps and gap_responses:
            gap_section += "  - Identified Gaps and User Responses:\n"

            for i, gap in enumerate(gaps):
                gap_key = f"gap_{i}"
                if gap_key in gap_responses:
                    gap_type = gap.get('type', 'general')
                    gap_description = gap.get('description', 'No description')
                    user_response = gap_responses[gap_key].get('response', '')

                    gap_section += f"    * {gap_type.title()}: {gap_description}\n"
                    gap_section += f"      User Response: {user_response}\n"

        # Add additional instructions if provided
        additional_instructions = gap_responses.get('additional_instructions', '')
        if additional_instructions:
            gap_section += f"  - Additional User Instructions: {additional_instructions}\n"

        # Add recommendations
        recommendations = gap_analysis_data.get('recommendations', [])
        if recommendations:
            gap_section += "  - AI Recommendations:\n"
            for rec in recommendations:
                gap_section += f"    * {rec}\n"

        gap_section += "\n  IMPORTANT: Use this gap analysis information to enhance the generated script and address the identified gaps."

        return gap_section

    except Exception as e:
        logger.error(f"Failed to build gap analysis enhancement section: {e}")
        return ""
